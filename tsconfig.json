{"compilerOptions": {"target": "ES2017", "lib": ["dom", "dom.iterable", "esnext"], "allowJs": true, "skipLibCheck": true, "strict": true, "noEmit": true, "esModuleInterop": true, "module": "esnext", "moduleResolution": "bundler", "resolveJsonModule": true, "isolatedModules": true, "jsx": "preserve", "types": ["node"], "incremental": true, "baseUrl": ".", "plugins": [{"name": "next"}], "paths": {"@/*": ["./*"], "@server-lib/*": ["app/server-lib/*"]}}, "include": ["next-env.d.ts", "**/*.ts", "**/*.tsx", ".next/types/**/*.ts", "electron-main.ts", "src/types", "src", "**/*.d.ts", "next.config.ts"], "exclude": ["node_modules"]}