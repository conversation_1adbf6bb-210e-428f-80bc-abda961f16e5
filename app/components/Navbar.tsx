"use client";

import Image from "next/image";
import {useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import ConfigModal from "@/app/components/ConfigModal";
import { useAuth } from "@/app/context/AuthProvider";

// const DEEPSEEKMINE_OFFICIAL_URL: string = process.env.DEEPSEEKMINE_OFFICIAL_URL || "http://127.0.0.1:3001"
// const DEEPSEEKMINE_OFFICIAL_URL: string = process.env.NEXT_PUBLIC_DEEPSEEKMINE_OFFICIAL_URL || "http://127.0.0.1:3001"
const DEEPSEEKMINE_OFFICIAL_URL: string = "https://deepseekmine.com"
const isElectron = typeof window !== "undefined" && window.require;
const ipcRenderer = isElectron ? window.require("electron").ipcRenderer : null;

// 安全地获取 Electron shell API
const getElectronShell = () => {
    try {
        if (typeof window !== "undefined" && window.require) {
            const { shell } = window.require("electron");
            return shell;
        }
    } catch (error) {
        console.warn("无法获取 Electron shell API:", error);
    }
    return null;
};

export default function Navbar() {
    const router = useRouter();
    const { isLoggedIn, logout, isAdmin } = useAuth();

    const [showConfigModal, setShowConfigModal] = useState(false);
    const [version, setVersion] = useState("2.0.0");

    useEffect(() => {
        if (!ipcRenderer) return;
        const updateVersionHandler = (event: any, newVersion: string) => {
            setVersion(newVersion);
        };
        ipcRenderer.on("update-version", updateVersionHandler);
        return () => {
            ipcRenderer.removeListener("update-version", updateVersionHandler);
        };
    }, []);

    const handleOpenPayPage = () => {
        const url = `${DEEPSEEKMINE_OFFICIAL_URL}/pay`;

        try {
            // 优先使用 Electron shell API 在外部浏览器中打开（类似 Cursor 的实现）
            const shell = getElectronShell();
            if (shell) {
                console.log("正在外部浏览器中打开套餐升级页面...");
                shell.openExternal(url);
                return;
            }

            // 如果在浏览器环境中，使用 window.open
            console.log("正在新标签页中打开套餐升级页面...");
            window.open(url, "_blank");
        } catch (error) {
            console.error("打开套餐升级页面失败:", error);
            // 提供备用方案：复制链接到剪贴板
            if (navigator.clipboard) {
                navigator.clipboard.writeText(url).then(() => {
                    alert(`无法自动打开浏览器，链接已复制到剪贴板：\n${url}`);
                }).catch(() => {
                    alert(`请手动访问：${url}`);
                });
            } else {
                alert(`请手动访问：${url}`);
            }
        }
    };


    return (
        <header className="flex items-center justify-between py-4 px-6 bg-white shadow-sm">
            <div
                className="flex items-center space-x-2 cursor-pointer"
                onClick={() => router.push("/")}
            >
                <Image src="/logo.png" alt="Logo" width={28} height={28} />
                <span className="text-xs text-muted-foreground">v{version}</span>
            </div>

            <div className="flex items-center space-x-4">
                <button
                    className="text-sm rounded-md px-3 py-2 hover:bg-gray-100 transition"
                    onClick={() => router.push("/")}
                >
                    首页
                </button>

                <button
                    className="text-sm rounded-md px-3 py-2 hover:bg-gray-100 transition"
                    onClick={() => setShowConfigModal(true)}
                >
                    本地
                </button>

                {/* <button
                    className="text-sm rounded-md px-3 py-2 hover:bg-gray-100 transition"
                    onClick={() => router.push("/usermanual")}
                >
                    用户手册
                </button> */}

                <button
                    className="text-sm rounded-md px-3 py-2 bg-blue-600 text-white hover:bg-blue-700 transition-colors font-medium"
                    onClick={handleOpenPayPage}
                    title="升级到满血大模型套餐"
                >
                    升级套餐
                </button>

                {/* <button
                    className="text-sm rounded-md px-3 py-2 hover:bg-gray-100 transition"
                    onClick={() => router.push("/register")}
                >
                    注册
                </button> */}

                {isLoggedIn ? (
                    <>
                        <button
                            className="text-sm rounded-md px-3 py-2 hover:bg-gray-100 transition"
                            onClick={() => router.push("/dashboard")}
                        >
                            我
                        </button>
                        <button
                            className="text-sm rounded-md px-3 py-2 hover:bg-gray-100 transition"
                            onClick={async () => {
                                await logout();
                                router.push("/");
                                router.refresh();
                            }}
                        >
                            退出
                        </button>
                    </>
                ) : (
                    <button
                        className="text-sm rounded-md px-3 py-2 hover:bg-gray-100 transition"
                        onClick={() => router.push("/login")}
                    >
                        登录
                    </button>
                )}

                {
                    (
                        <button
                            className="text-sm rounded-md px-3 py-2 hover:bg-gray-100 transition"
                            onClick={() => router.push("/feedback")}
                        >
                            反馈
                        </button>
                    )
                }
                {/* <button
                    className="text-sm rounded-md px-3 py-2 hover:bg-gray-100 transition"
                    onClick={() => router.push("/feedbackList")}
                >
                    管理反馈
                </button> */}

                {showConfigModal && <ConfigModal onClose={() => setShowConfigModal(false)} />}
            </div>
        </header>
    );
}
