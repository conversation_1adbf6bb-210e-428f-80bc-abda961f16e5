"use client";

import { useEffect, useState } from "react";
import { useAuth } from "@/app/context/AuthProvider";
import {User, Calendar<PERSON>heck, Coins} from "lucide-react";

interface UsageInfo {
    status: number;
    message: string;
    username: string;
    used_count: number;
    total_count: number;
    remaining_count: number;
    orders: string[];
    start_date: string;
    end_date: string;
}

// const CHATGPT_END_URL = process.env.NEXT_PUBLIC_CHATGPT_END_URL || "http://127.0.0.1:6001"

const CHATGPT_END_URL: string = "https://deepseekmine.com"

export default function Dashboard() {
    const { username } = useAuth();
    const [usage, setUsage] = useState<UsageInfo | null>(null);

    useEffect(() => {
        async function fetchUsage() {
            try {
                const token = localStorage.getItem("access_token");
                const res = await fetch(`${CHATGPT_END_URL}/omni/api/chat/usage/`, {
                    method: "GET",
                    headers: {
                        "Authorization": `Bearer ${token}`,
                        "Content-Type": "application/json",
                    },
                });
                const data = await res.json();
                if (res.ok) {
                    setUsage(data);
                } else {
                    console.error("获取使用情况失败", data.detail || data.message);
                }
            } catch (err) {
                console.error("请求失败:", err);
            }
        }

        fetchUsage();
    }, []);

    return (
        <div className="container mx-auto p-6">
            <div className="bg-white shadow-md rounded-lg p-6 mb-6">
                <h2 className="text-2xl font-bold text-gray-800 flex items-center space-x-2">
                    <User className="w-6 h-6 text-indigo-500" />
                    <span>个人中心</span>
                </h2>

                <div className="mt-4 space-y-4 text-gray-700">
                    <p className="flex items-center">
                        <User className="w-5 h-5 text-blue-500 mr-2" />
                        <strong>用户名：</strong>{usage?.username || username || "未设置"}
                    </p>
                    <p className="flex items-center">
                        <Coins className="w-5 h-5 text-yellow-500 mr-1" /> {/* 这里添加金币图标 */}
                        <strong>剩余金币数：</strong>
                        {usage ? usage.remaining_count : "加载中..."}
                    </p>
                    <p className="flex items-center">
                        <Coins className="w-5 h-5 text-yellow-500 mr-1" /> {/* 这里添加金币图标 */}
                        <strong>总金币数：</strong>{usage?.total_count ?? "-"}，
                        已用：{usage?.used_count ?? "-"}
                    </p>
                    <p className="flex items-center">
                        <CalendarCheck className="w-5 h-5 text-teal-600 mr-2" />
                        <strong>套餐周期：</strong>
                        {usage?.start_date} ~ {usage?.end_date}
                    </p>
                </div>
            </div>


            <div className="bg-white shadow-md rounded-lg p-6">
                <h2 className="text-xl font-bold text-gray-800">更多功能</h2>
                <p className="text-gray-600 mt-2">更多功能开发中，敬请期待。</p>
            </div>
        </div>
    );
}
