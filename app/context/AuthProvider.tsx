"use client";

import { createContext, useContext, useEffect, useState } from "react";
import { useRouter } from "next/navigation";

interface AuthContextType {
    isLoggedIn: boolean;
    isAdmin: boolean;
    userId: number;
    username: string;
    plan: string;
    setIsLoggedIn: (value: boolean) => void;
    setIsAdmin: (value: boolean) => void;
    setUserId: (value: number) => void;
    setUsername: (value: string) => void;
    setPlan: (value: string) => void;
    logout: () => void;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function AuthProvider({ children }: { children: React.ReactNode }) {
    const [isLoggedIn, setIsLoggedIn] = useState(false);
    const [isAdmin, setIsAdmin] = useState(false);
    const [userId, setUserId] = useState(0);
    const [username, setUsername] = useState("");   // myy0524修改
    const [plan, setPlan] = useState("");           // myy0524修改
    const router = useRouter();

    useEffect(() => {
        // 检查 localStorage 中记录的登录状态及登录时间（24h 有效期）
        const storedIsLogin = localStorage.getItem("isLogin");
        const storedTimestamp = localStorage.getItem("loginTimestamp");
        const now = Date.now();
        // 注：24小时 = 60s * 60（min）* 24（h）*1000（ms） * 60 * 24 * 1000
        if (storedIsLogin === "true" && storedTimestamp && now - Number(storedTimestamp) < 60 * 60 * 24 * 1000) {
            setIsLoggedIn(true);
        } else {
            localStorage.removeItem("isLogin");
            localStorage.removeItem("loginTimestamp");
            setIsLoggedIn(false);
        }

        // async function fetchUser() {
        //     try {
        //         // 调用本地代理，确保 credentials 被包含
        //         const res = await fetch("https://zglg.work/api/auth/me", { credentials: "include" });
        //         if (!res.ok) return;
        //
        //         const data = await res.json();
        //         if (data?.user) {
        //             setIsLoggedIn(true);
        //             setUserId(Number(data.user.id));
        //             setUsername(data.user.username || "");
        //             setPlan(data.user.plan || "");
        //             if (data.user.user_type && data.user.user_type === 2) {
        //                 setIsAdmin(true);
        //             } else {
        //                 setIsAdmin(false);
        //             }
        //             // 更新 localStorage 中的登录状态及时间戳
        //             localStorage.setItem("isLogin", "true");
        //             localStorage.setItem("loginTimestamp", Date.now().toString());
        //         }
        //     } catch (error) {
        //         console.error("[AuthProvider] 获取用户失败:", error);
        //     }
        // }

        // fetchUser();
    }, []);

    const logout = () => {
        document.cookie = "token=; path=/; expires=Thu, 01 Jan 1970 00:00:00 UTC;";
        // fetch("https://zglg.work/api/auth/logout", { method: "POST" });
        fetch("/api/auth/logout", { method: "POST" });
        setIsLoggedIn(false);
        setIsAdmin(false);
        setUsername("");
        setPlan("");
        // 清除 localStorage 中的登录状态及时间戳
        localStorage.removeItem("isLogin");
        localStorage.removeItem("loginTimestamp");
        localStorage.removeItem("access_token");
        router.push("/login");
    };

    return (
        <AuthContext.Provider value={{
            isLoggedIn,
            isAdmin,
            userId,
            username,
            plan,
            setIsLoggedIn,
            setIsAdmin,
            setUserId,
            setUsername,
            setPlan,
            logout
        }}>
            {children}
        </AuthContext.Provider>
    );
}

export function useAuth() {
    const context = useContext(AuthContext);
    if (!context) {
        throw new Error("useAuth must be used within an AuthProvider");
    }
    return context;
}
