{"name": "DeepSeekMine", "version": "2.0.0", "description": "DeepSeekMine个人知识库", "author": "<PERSON>hen <NAME_EMAIL>", "main": "dist_electron/electron.main.prod.cjs", "type": "module", "license": "MIT", "scripts": {"dev": "cross-env NODE_ENV=development concurrently \"npm run dev:next\" \"npm run dev:electron\"", "dev:next": "npx next dev -p 3335", "dev:electron": "wait-on http://localhost:3335 && cross-env NODE_ENV=development electron dist_electron/electron.main.dev.cjs", "build": "next build", "start": "next start", "dist": "npm run clean && npm install && npm run build && electron-builder", "dist:win": "npm install && npm run build && electron-builder --win --publish always", "dist:win:x64": "npm run build && electron-builder --win --x64", "dist:win:arm64": "npm run build && electron-builder --win --arm64", "clean": "rimraf .next dist .turbo .next/cache && npm run clean:build"}, "devDependencies": {"@eslint/eslintrc": "^3", "@types/dotenv": "^6.1.1", "@types/file-saver": "^2.0.7", "@types/formidable": "^3.4.5", "@types/node": "^20.17.19", "@types/react": "^19", "@types/react-dom": "^19", "@types/uuid": "^10.0.0", "concurrently": "^7.6.0", "cross-env": "^7.0.3", "electron": "^25.9.8", "electron-builder": "^23.6.0", "rimraf": "^6.0.1", "tailwindcss": "^3.4.1", "ts-node": "^10.9.2", "typescript": "^5.8.3", "wait-on": "^7.2.0"}, "dependencies": {"@node-rs/jieba": "^2.0.1", "axios": "^1.8.4", "csv-parse": "^6.1.0", "docx": "^9.5.1", "electron-updater": "^6.6.2", "file-saver": "^2.0.5", "form-data": "^4.0.2", "formidable": "^3.5.2", "franc": "^6.2.0", "lucide-react": "^0.477.0", "mammoth": "^1.9.1", "meilisearch": "^0.51.0", "mysql2": "^3.12.0", "next": "^15.2.0", "pdf-parse": "^1.1.1", "pdfreader": "^3.0.7", "react": "^19", "react-dom": "^19", "react-markdown": "^10.0.1", "react-syntax-highlighter": "^15.6.1", "remark-gfm": "^4.0.1", "shiki": "^3.1.0", "uuid": "^11.1.0", "winston": "^3.17.0", "winston-daily-rotate-file": "^5.0.0", "xlsx": "^0.18.5"}, "engines": {"node": ">= 12.0.0"}, "build": {"appId": "com.deepseekmine", "productName": "DeepSeekMine", "icon": "public/logo.png", "directories": {"output": "dist"}, "publish": [{"provider": "generic", "url": "https://deepseekmine.com/omni/api/downloads/"}], "asar": true, "asarUnpack": ["meilisearch/**", "node-mac/**", "node-windows/**", "node_modules/next/**", "node_modules/styled-jsx/**", "node_modules/react/**", "node_modules/react-dom/**", "node_modules/@next/**", "node_modules/@swc/helpers/**", "node_modules/caniuse-lite/**", "node_modules/browserslist/**"], "files": ["dist_electron/**/*", "public/**/*", "next.config.js", "package.json", "!**/*.pdb", "!**/*.dll"], "extraResources": [{"from": ".next", "to": "app/.next"}, {"from": "app/baidu_stopwords.txt", "to": "app/app/baidu_stopwords.txt"}, {"from": "app/scripts", "to": "app/app/scripts"}, {"from": "test", "to": "app/test"}, {"from": "public", "to": "app/public"}, {"from": "meilisearch", "to": "app/meilisearch"}, {"from": "node-windows", "to": "app/node-windows"}, {"from": "node_modules/next", "to": "app/node_modules/next"}, {"from": "node_modules/react", "to": "app/node_modules/react"}, {"from": "node_modules/react-dom", "to": "app/node_modules/react-dom"}, {"from": "node_modules/@next", "to": "app/node_modules/@next"}, {"from": "node_modules/@swc", "to": "app/node_modules/@swc"}, {"from": "node_modules/postcss", "to": "app/node_modules/postcss"}, {"from": "node_modules/styled-jsx", "to": "app/node_modules/styled-jsx"}, {"from": "node_modules/axios", "to": "app/node_modules/axios"}, {"from": "node_modules/winston", "to": "app/node_modules/winston"}, {"from": "node_modules/formidable", "to": "app/node_modules/formidable"}, {"from": "node_modules/lucide-react", "to": "app/node_modules/lucide-react"}, {"from": "node_modules/tailwindcss", "to": "app/node_modules/tailwindcss"}, {"from": "node_modules/remark-gfm", "to": "app/node_modules/remark-gfm"}, {"from": "node_modules/shiki", "to": "app/node_modules/shiki"}, {"from": "node_modules/@node-rs", "to": "app/node_modules/@node-rs"}, {"from": "node_modules/react-markdown", "to": "app/node_modules/react-markdown"}, {"from": "node_modules/react-syntax-highlighter", "to": "app/node_modules/react-syntax-highlighter"}], "mac": {"target": "dmg", "artifactName": "DeepSeekMine-${version}-${arch}.${ext}"}, "win": {"target": [{"target": "nsis", "arch": ["x64", "arm64"]}], "artifactName": "DeepSeekMine-${version}-${arch}.${ext}"}, "linux": {"target": [{"target": "AppImage", "arch": ["x64", "arm64"]}], "artifactName": "DeepSeekMine-${version}-${arch}.${ext}", "category": "Utility"}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true, "differentialPackage": true}}}