## 非常重要1
默认按照下面配置后为dev模式，若想启动Docker打包模式， 请按照如下修改：

修改文件 semantic_rag/configure.py文件中的 ENV = "docker" 

然后，再配置Docker启动步骤，即可。

### 非常重要2
辛苦同学们，先尽快在本地跑通dev环境，这样大家就能进入开发和测试了。1）大家在上传代码时，必须要先在本地自己测试验证通过，2）然后再上传到dev分支，大家协作开发，我们统一都在dev分支上操作（这样后期是最容易合并代码）3）提交Pull request到main分支，然后测试同学会接入测试验证。


## 1 本地开发环境dev启动方法

DeepSeekMine 本地开发依赖以下环境：

- Python 3.10+
- Meilisearch v1.13.2（推荐使用二进制）
- Node.js 18+

---

### 1.1 创建并激活 Python 虚拟环境

```bash
cd ./semantic_rag
python -m venv python_env
```

Windows: 

```bash
python_env\Scripts\activate
```

Mac/Linux: 

```bash
source python_env/bin/activate
```

```bash
pip install --upgrade pip
pip install -r requirements.txt
```

---

### 1.2 下载并启动 Meilisearch

下载meilisearch二进制文件，并放到目录semantic_rag文件夹下。

Windows 下载地址：https://github.com/meilisearch/meilisearch/releases/download/v1.13.2/meilisearch-windows-amd64.exe

---

下载后Mac电脑，修改文件名为：meilisearch

Mac M系列下载地址：https://github.com/meilisearch/meilisearch/releases/download/v1.13.2/meilisearch-macos-apple-silicon

Mac Intel系列下载地址：https://github.com/meilisearch/meilisearch/releases/download/v1.13.2/meilisearch-macos-amd64

Linux AMD64: https://github.com/meilisearch/meilisearch/releases/download/v1.13.2/meilisearch-linux-amd64

若Mac电脑启动出现meilisearch权限问题，参考下面步骤解决：

```bash
# Mac/Linux 赋予执行权限
chmod +x meilisearch
xattr -d com.apple.quarantine ./meilisearch  # 若提示权限问题

# 启动
./meilisearch --master-key qaz0913cde350odxs
```

### 1.3 Node安装

要安装 Node.js，Windows 用户可前往官网 [https://nodejs.org/](https://nodejs.org/) 下载推荐的 LTS 版本安装包，双击安装后通过 `node -v` 和 `npm -v` 验证安装是否成功。macOS 用户推荐使用 Homebrew 执行 `brew install node` 安装，Linux（如 Ubuntu）用户可使用官方脚本：`curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -` 然后执行 `sudo apt install -y nodejs`。安装完成后，使用 `node -v` 和 `npm -v` 检查版本是否正确，建议使用 Node.js 18.x LTS 版本以获得最佳兼容性和长期支持。


---

### 1.4 启动DeepSeekMine
先安装nextjs的依赖执行，这些依赖是从package.json中读取，运行命令：
```bash
npm install
```

以上依赖全部做好后，后面再次启动就不必重复，直接执行下面命令即可：

```bash
npm run dev
```

---

## 2 镜像启动DeepSeekMine步骤

镜像启动DeepSeekMine三个步骤：

第一，修改文件 semantic_rag/config.py文件中的 ENV = "docker"  #  默认为 "dev"

若是Windows系统，先使用下面命令，创建路径：

```bash
mkdir C:\deepseekmine\meili_data
```

再设置 docker-compose.yml文件的meilisearch下面的volume为：

volumes:
      - "C:/deepseekmine/meili_data:/meili_data"

第二，执行镜像制作：docker build -t deepseekmine:0.7.1 .

第三，启动镜像方法，执行：docker-compose up 

后台执行：docker-compose up -d

---

### 2.1 打包DeepSeek镜像步骤

若以上验证没问题后，现在即可打包镜像了：

MAC一键打给客户镜像包，执行下面命令：

```sh
sh package-all.sh
```

Win一键打给客户镜像包：双击 package-all.bat

## 3 合并代码规范

在进行代码合并前，开发人员必须先在本地完成充分的功能测试，确保代码逻辑正确且不影响现有功能，所有单元测试应全部通过，且无明显的异常日志或 Bug。经过自测确认无误后，方可将代码提交至远程 `dev` 分支，提交时需使用规范且清晰的提交信息。例如，通过 `git add .`、`git commit -m "feat: 优化文档搜索功能，提升准确率"` 和 `git push origin dev` 提交代码。

提交至 `dev` 分支后，应立即创建 Pull Request（PR），申请代码合并。在创建 PR 时，需确保标题简洁明确，且详细描述本次变更的功能点、影响范围、测试情况以及需要特别说明的注意事项。PR 提交后，由测试同学负责进行功能验收和代码审核，包括功能完整性验证、无严重 Bug、性能符合预期以及兼容性测试等。验收通过后，由代码负责人或指定人员执行合并操作，推荐使用 **Squash and Merge** 策略，以保持主分支的提交历史简洁清晰。

合并完成后，需确认主分支代码稳定，并按照发布计划上线。上线前应遵循公司或团队的上线审批流程，并准备好回滚方案以防止突发问题。在发布新版本时，必须打上对应的 Git Tag 以便于版本管理和问题追溯，例如通过 `git tag -a v1.0.0 -m "版本发布说明"` 和 `git push origin v1.0.0` 完成版本标记。整个流程中，严禁直接向主分支（`main`）提交代码，且 PR 中不得包含调试代码、未使用的变量或测试日志，以确保代码质量和系统稳定性。



## 4 Docker打包注意事项

1. Python后端semantic_rag是否新增py文件了，若增加了需要对应添加pyc文件导出，格式如下：

```
COPY --from=pybuilder /build/semantic_rag/__pycache__/ocr_handle.cpython-310.pyc semantic_rag/ocr_handle.pyc
```

## 5 安装Docker常见问题

如果在Mac电脑安装后，总是出现Malware问题，请参考以下步骤解决。

首先在终端执行下面命令：

```bash
sudo launchctl bootout system/com.docker.vmnetd 2>/dev/null || true
sudo launchctl bootout system/com.docker.socket 2>/dev/null || true

sudo rm /Library/PrivilegedHelperTools/com.docker.vmnetd || true
sudo rm /Library/PrivilegedHelperTools/com.docker.socket || true

ps aux | grep -i docker | awk '{print $2}' | sudo xargs kill -9 2>/dev/null
```
然后再从这里下载：https://docs.docker.com/desktop/release-notes/#4372

安装后就可以，测试电脑：Mac M1 Pro笔记本，经过以上步骤后解决。


### New Items

跨平台打包时更稳定，可以在你的 Electron 项目根目录下加一条构建时自动赋权的脚本：
// package.json scripts
"postinstall": "chmod +x ./meilisearch"

